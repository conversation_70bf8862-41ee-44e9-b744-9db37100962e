"""
Visualization module for the pheromone-based maze navigation system.

This module provides real-time visualization of the maze environment, agent movement,
pheromone fields, and navigation statistics using matplotlib.
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib.animation as animation
from matplotlib.colors import LinearSegmentedColormap, Normalize
from matplotlib.patches import Rectangle
from typing import Dict, List, Tuple, Optional, Any
import time

from .config import SimulationConfig
from .maze import Maze
from .agent import Agent
from .pheromone_field import PheromoneField


class Visualizer:
    """
    Real-time visualization system for maze navigation simulation.

    Provides comprehensive visualization including:
    - Maze layout with obstacles and target
    - Agent position and movement path
    - Pheromone field heatmaps (obstacle and target pheromones)
    - Navigation statistics and performance metrics
    """

    def __init__(self, maze: Maze, pheromone_field: PheromoneField, config: Optional[SimulationConfig] = None):
        """
        Initialize the visualizer with maze and pheromone field references.

        Args:
            maze: The maze environment to visualize
            pheromone_field: The pheromone field manager
            config: Simulation configuration (optional)
        """
        self.maze = maze
        self.pheromone_field = pheromone_field
        self.config = config or SimulationConfig()

        # Visualization state
        self.fig = None
        self.axes = {}
        self.artists = {}
        self.animation_frames = []
        self.is_initialized = False

        # Color maps for different visualizations
        self.obstacle_colormap = self._create_obstacle_colormap()
        self.target_colormap = self._create_target_colormap()
        self.combined_colormap = self._create_combined_colormap()

        # Statistics tracking
        self.stats_history = {
            'steps': [],
            'success_rate': [],
            'collision_count': [],
            'pheromone_strength': []
        }

    def _create_obstacle_colormap(self) -> LinearSegmentedColormap:
        """Create colormap for obstacle pheromones (negative values)."""
        colors = ['darkred', 'red', 'orange', 'white']
        return LinearSegmentedColormap.from_list('obstacle', colors, N=256)

    def _create_target_colormap(self) -> LinearSegmentedColormap:
        """Create colormap for target pheromones (positive values)."""
        colors = ['white', 'lightgreen', 'green', 'darkgreen']
        return LinearSegmentedColormap.from_list('target', colors, N=256)

    def _create_combined_colormap(self) -> LinearSegmentedColormap:
        """Create colormap for combined pheromone field."""
        colors = ['darkred', 'red', 'white', 'lightgreen', 'darkgreen']
        return LinearSegmentedColormap.from_list('combined', colors, N=256)

    def initialize_display(self, show_pheromones: bool = True, show_separate_fields: bool = False) -> None:
        """
        Initialize the matplotlib display with subplots.

        Args:
            show_pheromones: Whether to show pheromone field visualizations
            show_separate_fields: Whether to show separate obstacle/target fields
        """
        if self.is_initialized:
            return

        # Determine subplot layout
        if show_pheromones and show_separate_fields:
            # 2x2 layout: maze, combined field, obstacle field, target field
            self.fig, axes_array = plt.subplots(2, 2, figsize=(12, 10))
            self.axes = {
                'maze': axes_array[0, 0],
                'combined': axes_array[0, 1],
                'obstacle': axes_array[1, 0],
                'target': axes_array[1, 1]
            }
        elif show_pheromones:
            # 1x2 layout: maze and combined pheromone field
            self.fig, axes_array = plt.subplots(1, 2, figsize=(12, 5))
            self.axes = {
                'maze': axes_array[0],
                'combined': axes_array[1]
            }
        else:
            # Single plot: maze only
            self.fig, ax = plt.subplots(1, 1, figsize=(8, 8))
            self.axes = {'maze': ax}

        # Configure each subplot
        self._setup_maze_plot()
        if 'combined' in self.axes:
            self._setup_pheromone_plot('combined', 'Combined Pheromone Field')
        if 'obstacle' in self.axes:
            self._setup_pheromone_plot('obstacle', 'Obstacle Pheromones')
        if 'target' in self.axes:
            self._setup_pheromone_plot('target', 'Target Pheromones')

        # Add overall title and layout
        self.fig.suptitle('Pheromone-Based Maze Navigation', fontsize=16)
        plt.tight_layout()

        self.is_initialized = True

    def _setup_maze_plot(self) -> None:
        """Setup the main maze visualization subplot."""
        ax = self.axes['maze']
        ax.set_title('Maze Environment')
        ax.set_xlim(-0.5, self.maze.width - 0.5)
        ax.set_ylim(-0.5, self.maze.height - 0.5)
        ax.set_aspect('equal')
        ax.invert_yaxis()  # Match array indexing (y=0 at top)

        # Draw maze grid
        for i in range(self.maze.width + 1):
            ax.axvline(i - 0.5, color='lightgray', linewidth=0.5)
        for i in range(self.maze.height + 1):
            ax.axhline(i - 0.5, color='lightgray', linewidth=0.5)

        # Draw obstacles
        obstacle_patches = []
        for y in range(self.maze.height):
            for x in range(self.maze.width):
                if self.maze.is_obstacle(x, y):
                    rect = Rectangle((x-0.5, y-0.5), 1, 1,
                                   facecolor='black', edgecolor='gray')
                    ax.add_patch(rect)
                    obstacle_patches.append(rect)

        # Draw target position
        if self.maze.target_position:
            target_x, target_y = self.maze.target_position
            target_circle = plt.Circle((target_x, target_y), 0.3,
                                     color='gold', zorder=10)
            ax.add_patch(target_circle)
            self.artists['target'] = target_circle

        # Initialize agent position marker (will be updated in render_frame)
        agent_circle = plt.Circle((0, 0), 0.2, color='blue', zorder=15)
        ax.add_patch(agent_circle)
        self.artists['agent'] = agent_circle

        # Initialize path line (will be updated in render_frame)
        path_line, = ax.plot([], [], 'b-', alpha=0.6, linewidth=2, zorder=5)
        self.artists['path'] = path_line

    def _setup_pheromone_plot(self, plot_type: str, title: str) -> None:
        """Setup a pheromone field visualization subplot."""
        ax = self.axes[plot_type]
        ax.set_title(title)
        ax.set_xlim(-0.5, self.maze.width - 0.5)
        ax.set_ylim(-0.5, self.maze.height - 0.5)
        ax.set_aspect('equal')
        ax.invert_yaxis()

        # Initialize empty heatmap (will be updated in render_frame)
        empty_field = np.zeros((self.maze.height, self.maze.width))

        if plot_type == 'obstacle':
            colormap = self.obstacle_colormap
            vmin, vmax = -20, 0
        elif plot_type == 'target':
            colormap = self.target_colormap
            vmin, vmax = 0, 20
        else:  # combined
            colormap = self.combined_colormap
            vmin, vmax = -20, 20

        im = ax.imshow(empty_field, cmap=colormap, vmin=vmin, vmax=vmax,
                      extent=[-0.5, self.maze.width-0.5, self.maze.height-0.5, -0.5])
        self.artists[f'{plot_type}_heatmap'] = im

        # Add colorbar
        cbar = plt.colorbar(im, ax=ax, shrink=0.8)
        cbar.set_label('Pheromone Intensity')
        self.artists[f'{plot_type}_colorbar'] = cbar

    def render_frame(self, agent: Agent, show_values: bool = False) -> None:
        """
        Render a single frame of the visualization.

        Args:
            agent: The agent to visualize
            show_values: Whether to show numerical pheromone values
        """
        if not self.is_initialized:
            self.initialize_display()

        # Update agent position
        agent_pos = agent.get_position()
        self.artists['agent'].center = agent_pos

        # Update agent path
        path_history = agent.get_move_history()
        if len(path_history) > 1:
            path_x = [pos[0] for pos in path_history]
            path_y = [pos[1] for pos in path_history]
            self.artists['path'].set_data(path_x, path_y)

        # Update pheromone field visualizations
        if 'combined_heatmap' in self.artists:
            self._update_pheromone_heatmap('combined', self.pheromone_field.combined_field)

        if 'obstacle_heatmap' in self.artists:
            self._update_pheromone_heatmap('obstacle', self.pheromone_field.obstacle_field)

        if 'target_heatmap' in self.artists:
            self._update_pheromone_heatmap('target', self.pheromone_field.target_field)

        # Add numerical values if requested
        if show_values and 'combined' in self.axes:
            self._add_pheromone_values('combined', self.pheromone_field.combined_field)

        # Update statistics display
        self._update_statistics_display(agent)

        # Refresh display
        plt.draw()
        plt.pause(self.config.visualization_delay)

    def _update_pheromone_heatmap(self, plot_type: str, field_data: np.ndarray) -> None:
        """Update a pheromone heatmap with new data."""
        heatmap = self.artists[f'{plot_type}_heatmap']
        heatmap.set_array(field_data)

        # Auto-adjust color scale based on actual data range
        if plot_type == 'obstacle':
            vmin = min(field_data.min(), -1)
            vmax = 0
        elif plot_type == 'target':
            vmin = 0
            vmax = max(field_data.max(), 1)
        else:  # combined
            abs_max = max(abs(field_data.min()), abs(field_data.max()), 1)
            vmin, vmax = -abs_max, abs_max

        heatmap.set_clim(vmin, vmax)

    def _add_pheromone_values(self, plot_type: str, field_data: np.ndarray) -> None:
        """Add numerical pheromone values as text annotations."""
        ax = self.axes[plot_type]

        # Clear previous text annotations
        for text in ax.texts:
            text.remove()

        # Add new text annotations
        for y in range(self.maze.height):
            for x in range(self.maze.width):
                value = field_data[y, x]
                if abs(value) > 0.1:  # Only show significant values
                    ax.text(x, y, f'{value:.1f}', ha='center', va='center',
                           fontsize=8, color='white' if abs(value) > 5 else 'black')

    def _update_statistics_display(self, agent: Agent) -> None:
        """Update statistics display in the figure."""
        # Update statistics history
        self.stats_history['steps'].append(agent.state.move_count)
        self.stats_history['collision_count'].append(agent.get_collision_count())

        # Calculate current pheromone strength
        current_perception = agent.perceive_all_directions()
        avg_strength = np.mean([v for v in current_perception.values() if v != float('-inf')])
        self.stats_history['pheromone_strength'].append(avg_strength)

        # Only add statistics text if figure is initialized
        if self.fig is not None:
            # Add statistics text to the figure
            stats_text = (
                f"Steps: {agent.state.move_count}\n"
                f"Collisions: {agent.get_collision_count()}\n"
                f"Position: {agent.get_position()}\n"
                f"Avg Pheromone: {avg_strength:.2f}"
            )

            # Add text box with statistics
            if hasattr(self, '_stats_text') and self._stats_text is not None:
                self._stats_text.remove()

            self._stats_text = self.fig.text(0.02, 0.98, stats_text,
                                            transform=self.fig.transFigure,
                                            verticalalignment='top',
                                            bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

    def show_pheromone_field(self, field_type: str = 'combined', show_values: bool = True) -> None:
        """
        Display a static view of the pheromone field.

        Args:
            field_type: Type of field to show ('combined', 'obstacle', 'target')
            show_values: Whether to show numerical values
        """
        if not self.is_initialized:
            self.initialize_display(show_pheromones=True)

        # Get the appropriate field data
        if field_type == 'obstacle':
            field_data = self.pheromone_field.obstacle_field
            title = 'Obstacle Pheromone Field'
            colormap = self.obstacle_colormap
        elif field_type == 'target':
            field_data = self.pheromone_field.target_field
            title = 'Target Pheromone Field'
            colormap = self.target_colormap
        else:
            field_data = self.pheromone_field.combined_field
            title = 'Combined Pheromone Field'
            colormap = self.combined_colormap

        # Create new figure for static display
        fig, ax = plt.subplots(figsize=(8, 8))
        ax.set_title(title)
        ax.set_xlim(-0.5, self.maze.width - 0.5)
        ax.set_ylim(-0.5, self.maze.height - 0.5)
        ax.set_aspect('equal')
        ax.invert_yaxis()

        # Display heatmap
        im = ax.imshow(field_data, cmap=colormap,
                      extent=[-0.5, self.maze.width-0.5, self.maze.height-0.5, -0.5])
        plt.colorbar(im, ax=ax, label='Pheromone Intensity')

        # Add numerical values if requested
        if show_values:
            for y in range(self.maze.height):
                for x in range(self.maze.width):
                    value = field_data[y, x]
                    if abs(value) > 0.1:
                        ax.text(x, y, f'{value:.1f}', ha='center', va='center',
                               fontsize=8, color='white' if abs(value) > 5 else 'black')

        # Draw obstacles and target
        for y in range(self.maze.height):
            for x in range(self.maze.width):
                if self.maze.is_obstacle(x, y):
                    rect = Rectangle((x-0.5, y-0.5), 1, 1,
                                   facecolor='none', edgecolor='red', linewidth=2)
                    ax.add_patch(rect)

        if self.maze.target_position:
            target_x, target_y = self.maze.target_position
            ax.scatter(target_x, target_y, c='gold', s=200, marker='*',
                      edgecolors='black', linewidth=2, zorder=10)

        plt.tight_layout()
        plt.show()

    def show_agent_path(self, agent: Agent, highlight_collisions: bool = True) -> None:
        """
        Display the agent's movement path with optional collision highlighting.

        Args:
            agent: The agent whose path to display
            highlight_collisions: Whether to highlight collision points
        """
        fig, ax = plt.subplots(figsize=(10, 8))
        ax.set_title('Agent Navigation Path')
        ax.set_xlim(-0.5, self.maze.width - 0.5)
        ax.set_ylim(-0.5, self.maze.height - 0.5)
        ax.set_aspect('equal')
        ax.invert_yaxis()

        # Draw maze background
        for y in range(self.maze.height):
            for x in range(self.maze.width):
                if self.maze.is_obstacle(x, y):
                    rect = Rectangle((x-0.5, y-0.5), 1, 1,
                                   facecolor='lightgray', edgecolor='gray')
                    ax.add_patch(rect)

        # Draw path
        path_history = agent.get_move_history()
        if len(path_history) > 1:
            path_x = [pos[0] for pos in path_history]
            path_y = [pos[1] for pos in path_history]
            ax.plot(path_x, path_y, 'b-', linewidth=2, alpha=0.7, label='Path')

            # Mark start and end positions
            ax.scatter(path_x[0], path_y[0], c='green', s=100, marker='o',
                      label='Start', zorder=10)
            ax.scatter(path_x[-1], path_y[-1], c='blue', s=100, marker='o',
                      label='Current', zorder=10)

        # Highlight collision points if requested
        if highlight_collisions and hasattr(agent, 'collision_history'):
            collision_positions = [pos for pos, _ in agent.collision_history]
            if collision_positions:
                collision_x = [pos[0] for pos in collision_positions]
                collision_y = [pos[1] for pos in collision_positions]
                ax.scatter(collision_x, collision_y, c='red', s=80, marker='x',
                          label='Collisions', zorder=15)

        # Draw target
        if self.maze.target_position:
            target_x, target_y = self.maze.target_position
            ax.scatter(target_x, target_y, c='gold', s=200, marker='*',
                      edgecolors='black', linewidth=2, label='Target', zorder=10)

        ax.legend()
        ax.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.show()

    def display_statistics(self, agent: Agent, success: bool, total_time: float = 0.0) -> None:
        """
        Display comprehensive navigation statistics.

        Args:
            agent: The agent to analyze
            success: Whether navigation was successful
            total_time: Total simulation time in seconds
        """
        # Calculate statistics
        steps = agent.state.move_count
        collisions = agent.get_collision_count()
        path_length = len(agent.get_move_history())

        # Calculate efficiency metrics
        if self.maze.target_position and self.maze.start_position:
            start_x, start_y = self.maze.start_position
            target_x, target_y = self.maze.target_position
            optimal_distance = abs(target_x - start_x) + abs(target_y - start_y)  # Manhattan distance
            efficiency = optimal_distance / max(path_length, 1) * 100
        else:
            efficiency = 0.0

        # Create statistics display
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(12, 10))
        fig.suptitle('Navigation Statistics', fontsize=16)

        # 1. Basic metrics
        ax1.axis('off')
        stats_text = f"""
        Navigation Result: {'SUCCESS' if success else 'FAILED'}
        Total Steps: {steps}
        Path Length: {path_length}
        Collisions: {collisions}
        Efficiency: {efficiency:.1f}%
        Time: {total_time:.2f}s
        """
        ax1.text(0.1, 0.9, stats_text, transform=ax1.transAxes, fontsize=12,
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightblue'))

        # 2. Step count over time
        if len(self.stats_history['steps']) > 1:
            ax2.plot(self.stats_history['steps'], label='Steps')
            ax2.set_title('Steps Over Time')
            ax2.set_xlabel('Simulation Step')
            ax2.set_ylabel('Cumulative Steps')
            ax2.grid(True, alpha=0.3)

        # 3. Collision frequency
        if len(self.stats_history['collision_count']) > 1:
            ax3.plot(self.stats_history['collision_count'], 'r-', label='Collisions')
            ax3.set_title('Collision Count Over Time')
            ax3.set_xlabel('Simulation Step')
            ax3.set_ylabel('Cumulative Collisions')
            ax3.grid(True, alpha=0.3)

        # 4. Pheromone strength evolution
        if len(self.stats_history['pheromone_strength']) > 1:
            ax4.plot(self.stats_history['pheromone_strength'], 'g-', label='Avg Pheromone')
            ax4.set_title('Average Pheromone Strength')
            ax4.set_xlabel('Simulation Step')
            ax4.set_ylabel('Pheromone Intensity')
            ax4.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.show()

    def save_animation(self, filename: str, agent_history: List[Agent] = None) -> None:
        """
        Save an animation of the navigation process.

        Args:
            filename: Output filename for the animation
            agent_history: List of agent states over time (if available)
        """
        if not self.animation_frames:
            print("No animation frames to save. Run simulation with frame recording enabled.")
            return

        # Create animation from recorded frames
        fig, ax = plt.subplots(figsize=(10, 8))

        def animate(frame_idx):
            ax.clear()
            # Render the frame (implementation would depend on stored frame data)
            # This is a placeholder - full implementation would require frame data storage
            ax.set_title(f'Navigation Animation - Frame {frame_idx}')
            return []

        anim = animation.FuncAnimation(fig, animate, frames=len(self.animation_frames),
                                     interval=100, blit=False)

        # Save animation
        try:
            if filename.endswith('.gif'):
                anim.save(filename, writer='pillow', fps=10)
            elif filename.endswith('.mp4'):
                anim.save(filename, writer='ffmpeg', fps=10)
            else:
                anim.save(filename + '.gif', writer='pillow', fps=10)
            print(f"Animation saved as {filename}")
        except Exception as e:
            print(f"Error saving animation: {e}")

    def record_frame(self, agent: Agent) -> None:
        """
        Record current state for animation creation.

        Args:
            agent: Current agent state to record
        """
        frame_data = {
            'agent_position': agent.get_position(),
            'path_history': agent.get_move_history().copy(),
            'pheromone_field': self.pheromone_field.combined_field.copy(),
            'step_count': agent.state.move_count,
            'collision_count': agent.get_collision_count()
        }
        self.animation_frames.append(frame_data)

    def clear_animation_frames(self) -> None:
        """Clear recorded animation frames."""
        self.animation_frames.clear()

    def close(self) -> None:
        """Close all matplotlib figures and clean up resources."""
        if self.fig:
            plt.close(self.fig)
        plt.close('all')
        self.is_initialized = False

    def set_interactive_mode(self, interactive: bool = True) -> None:
        """
        Set matplotlib interactive mode for real-time updates.

        Args:
            interactive: Whether to enable interactive mode
        """
        if interactive:
            plt.ion()
        else:
            plt.ioff()

    def get_statistics_summary(self) -> Dict[str, Any]:
        """
        Get a summary of collected statistics.

        Returns:
            Dictionary containing statistics summary
        """
        if not self.stats_history['steps']:
            return {}

        return {
            'total_steps': self.stats_history['steps'][-1] if self.stats_history['steps'] else 0,
            'total_collisions': self.stats_history['collision_count'][-1] if self.stats_history['collision_count'] else 0,
            'avg_pheromone_strength': np.mean(self.stats_history['pheromone_strength']) if self.stats_history['pheromone_strength'] else 0,
            'max_pheromone_strength': np.max(self.stats_history['pheromone_strength']) if self.stats_history['pheromone_strength'] else 0,
            'simulation_length': len(self.stats_history['steps'])
        }