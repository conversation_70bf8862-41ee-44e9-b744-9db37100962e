# Pheromone Maze Navigation System

A simulation system where agents navigate through mazes using pheromone gradient fields. Agents leave negative pheromones when hitting obstacles and are attracted to positive pheromones emitted by target points.

## Features

- **Intelligent Navigation**: Agents use pheromone gradients to make movement decisions
- **Dual Pheromone System**: Obstacle avoidance (negative) and target attraction (positive) pheromones
- **Real-time Visualization**: Interactive display of maze, agent movement, and pheromone fields
- **Configurable Parameters**: Extensive customization of maze, pheromone, and simulation parameters
- **Batch Experiments**: Run multiple experiments with statistical analysis
- **Multiple Output Formats**: JSON and YAML support for configuration and results

## Quick Start

### Interactive Demo
```bash
python main.py --demo
```

### Single Experiment
```bash
python main.py --width 15 --height 15 --obstacles 0.3
```

### Batch Experiments
```bash
python main.py --batch 50 --output results.json
```

### Using Configuration Files
```bash
python main.py --config configs/default.yaml --demo
```

## Command Line Options

### Execution Modes
- `--demo`: Run interactive demonstration with visualization
- `--batch N`: Run N batch experiments without visualization
- `--single`: Run single experiment (default)

### Configuration
- `--config FILE`: Load configuration from JSON or YAML file
- `--save-config FILE`: Save current configuration to file

### Maze Parameters
- `--width W`: Maze width (default: 20)
- `--height H`: Maze height (default: 20)
- `--obstacles RATIO`: Obstacle ratio 0.1-0.8 (default: 0.3)

### Pheromone Parameters
- `--decay-rate RATE`: Pheromone decay rate 0-1 (default: 0.95)
- `--diffusion-rate RATE`: Pheromone diffusion rate 0-1 (default: 0.1)
- `--obstacle-intensity INTENSITY`: Obstacle pheromone intensity (default: -10.0)
- `--target-intensity INTENSITY`: Target pheromone intensity (default: 15.0)

### Simulation Parameters
- `--max-steps STEPS`: Maximum steps per experiment (default: 1000)
- `--noise STD`: Decision noise standard deviation (default: 0.1)
- `--seed SEED`: Random seed for reproducible results

### Visualization Parameters
- `--no-visualization`: Disable visualization
- `--delay SECONDS`: Visualization delay (default: 0.1)
- `--show-pheromones`: Show pheromone fields in visualization
- `--show-values`: Show pheromone values in visualization

### Output Parameters
- `--output FILE`: Output file for results (JSON or YAML)
- `--verbose, -v`: Verbose output
- `--quiet, -q`: Quiet mode (minimal output)

## Configuration Files

The system supports both JSON and YAML configuration files. Example configurations are provided in the `configs/` directory:

- `configs/default.yaml`: Standard configuration
- `configs/small_maze.yaml`: Small maze for quick testing
- `configs/large_maze.yaml`: Large maze for complex experiments
- `configs/batch_experiment.json`: Optimized for batch processing

### Example Configuration (YAML)
```yaml
# Maze parameters
maze_width: 20
maze_height: 20
obstacle_ratio: 0.3

# Pheromone parameters
pheromone_decay_rate: 0.95
pheromone_diffusion_rate: 0.1
obstacle_pheromone_intensity: -10.0
target_pheromone_intensity: 15.0

# Simulation parameters
max_steps: 1000
visualization_delay: 0.1

# Agent parameters
decision_noise_std: 0.1
```

## Examples

### Basic Demo
```bash
# Run interactive demo with default settings
python main.py --demo

# Run demo with custom maze size
python main.py --demo --width 25 --height 25 --obstacles 0.4
```

### Single Experiments
```bash
# Quick experiment with small maze
python main.py --width 10 --height 10 --max-steps 200

# Experiment with custom pheromone parameters
python main.py --decay-rate 0.9 --diffusion-rate 0.15 --show-pheromones
```

### Batch Experiments
```bash
# Run 100 experiments and save results
python main.py --batch 100 --output experiment_results.json

# Batch with custom configuration
python main.py --config configs/large_maze.yaml --batch 50 --output large_maze_results.yaml

# Reproducible batch with seed
python main.py --batch 20 --seed 42 --output reproducible_results.json
```

### Configuration Management
```bash
# Save current settings to file
python main.py --width 15 --height 15 --obstacles 0.25 --save-config my_config.yaml

# Use saved configuration
python main.py --config my_config.yaml --demo
```

## Output Format

### Single Experiment Results
```json
{
  "experiment_id": "single",
  "success": true,
  "steps_taken": 45,
  "total_time": 2.3,
  "collision_count": 8,
  "path_length": 45,
  "path_efficiency": 0.67,
  "manhattan_distance": 30
}
```

### Batch Experiment Results
```json
{
  "batch_summary": {
    "batch_id": "abc12345",
    "total_experiments": 100,
    "success_rate": 0.85,
    "average_steps": 67.2,
    "average_efficiency": 0.72
  },
  "individual_results": [...]
}
```

## Algorithm Overview

The navigation system uses a dual pheromone mechanism:

1. **Obstacle Pheromones**: When agents collide with obstacles, they release negative pheromones that create repulsive fields
2. **Target Pheromones**: Target points continuously emit positive pheromones that create attractive fields
3. **Decision Making**: Agents sense pheromone gradients in four directions (WASD) and move toward the highest combined pheromone value
4. **Field Evolution**: Pheromones decay over time and diffuse to neighboring cells, creating dynamic gradient fields

## Testing

Run the comprehensive test suite:
```bash
python test_main_program.py
```

This tests all major features including command line parsing, configuration loading, experiment execution, and output generation.

## Requirements

- Python 3.7+
- NumPy
- Matplotlib
- PyYAML

Install dependencies:
```bash
pip install numpy matplotlib pyyaml
```
