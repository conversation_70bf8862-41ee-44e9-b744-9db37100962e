#!/usr/bin/env python3
"""
Test script for the visualization system.

This script tests all major visualization features including:
- Basic maze rendering
- Pheromone field visualization
- Agent path tracking
- Real-time updates
- Statistics display
"""

import sys
import time
import numpy as np
from pheromone_maze.config import SimulationConfig
from pheromone_maze.maze import <PERSON><PERSON>
from pheromone_maze.pheromone_field import Ph<PERSON>mone<PERSON><PERSON>
from pheromone_maze.agent import Agent
from pheromone_maze.visualizer import Visualizer


def test_basic_visualization():
    """Test basic visualization setup and static displays."""
    print("Testing basic visualization...")

    # Create test environment
    config = SimulationConfig(maze_width=8, maze_height=8, obstacle_ratio=0.2)
    maze = Maze(config.maze_width, config.maze_height, config.obstacle_ratio, seed=42)

    # Find valid start and target positions
    start_pos = None
    target_pos = None
    for y in range(maze.height):
        for x in range(maze.width):
            if not maze.is_obstacle(x, y):
                if start_pos is None:
                    start_pos = (x, y)
                elif target_pos is None and (x, y) != start_pos:
                    target_pos = (x, y)
                    break
        if target_pos is not None:
            break

    maze.set_start_position(start_pos[0], start_pos[1])
    maze.set_target_position(target_pos[0], target_pos[1])
    
    pheromone_field = PheromoneField(config.maze_width, config.maze_height, config)
    pheromone_field.set_target_position(target_pos[0], target_pos[1])
    pheromone_field.generate_target_pheromone_field()

    # Create agent and add some pheromones
    agent = Agent(start_pos[0], start_pos[1], maze, pheromone_field, config)
    
    # Simulate some movement and collisions to create pheromones
    agent.move('w')  # Should hit boundary
    agent.move('a')  # Should hit boundary
    pheromone_field.update_field()
    
    # Create visualizer
    visualizer = Visualizer(maze, pheromone_field, config)
    
    print("1. Testing static pheromone field display...")
    visualizer.show_pheromone_field('combined', show_values=True)
    
    print("2. Testing agent path display...")
    # Move agent a few steps
    for _ in range(5):
        agent.execute_pheromone_based_move()
        pheromone_field.update_field()
    
    visualizer.show_agent_path(agent, highlight_collisions=True)
    
    print("3. Testing statistics display...")
    visualizer.display_statistics(agent, success=False, total_time=1.5)
    
    visualizer.close()
    print("Basic visualization tests completed!")


def test_real_time_visualization():
    """Test real-time visualization during navigation."""
    print("\nTesting real-time visualization...")
    
    # Create test environment
    config = SimulationConfig(
        maze_width=10, 
        maze_height=10, 
        obstacle_ratio=0.25,
        visualization_delay=0.2,  # Slower for demonstration
        max_steps=50
    )
    
    maze = Maze(config.maze_width, config.maze_height, config.obstacle_ratio, seed=123)

    # Find valid positions
    start_pos = None
    target_pos = None
    for y in range(maze.height):
        for x in range(maze.width):
            if not maze.is_obstacle(x, y):
                if start_pos is None:
                    start_pos = (x, y)
                elif target_pos is None and abs(x - start_pos[0]) + abs(y - start_pos[1]) > 3:
                    target_pos = (x, y)
                    break
        if target_pos is not None:
            break

    if target_pos is None:  # Fallback if no distant target found
        for y in range(maze.height):
            for x in range(maze.width):
                if not maze.is_obstacle(x, y) and (x, y) != start_pos:
                    target_pos = (x, y)
                    break
            if target_pos is not None:
                break

    maze.set_start_position(start_pos[0], start_pos[1])
    maze.set_target_position(target_pos[0], target_pos[1])

    pheromone_field = PheromoneField(config.maze_width, config.maze_height, config)
    pheromone_field.set_target_position(target_pos[0], target_pos[1])
    pheromone_field.generate_target_pheromone_field()

    agent = Agent(start_pos[0], start_pos[1], maze, pheromone_field, config)
    
    # Create visualizer with real-time display
    visualizer = Visualizer(maze, pheromone_field, config)
    visualizer.set_interactive_mode(True)
    visualizer.initialize_display(show_pheromones=True, show_separate_fields=False)
    
    print("Running real-time navigation simulation...")
    print("Close the matplotlib window to continue...")
    
    # Run simulation with real-time visualization
    step_count = 0
    max_steps = 30  # Limit for demonstration
    
    try:
        while step_count < max_steps and not agent.is_at_target():
            # Record frame for potential animation
            visualizer.record_frame(agent)
            
            # Render current state
            visualizer.render_frame(agent, show_values=False)
            
            # Execute agent move
            success = agent.execute_pheromone_based_move()
            
            # Update pheromone field
            pheromone_field.update_field()
            
            step_count += 1
            
            # Check if we should continue
            if not success:
                print(f"Agent stuck at step {step_count}")
                break
        
        # Final render
        visualizer.render_frame(agent, show_values=True)
        
        # Show final statistics
        success = agent.is_at_target()
        print(f"\nSimulation completed:")
        print(f"  Success: {success}")
        print(f"  Steps: {step_count}")
        print(f"  Collisions: {agent.get_collision_count()}")
        
        # Display final statistics
        visualizer.display_statistics(agent, success, total_time=step_count * config.visualization_delay)
        
    except KeyboardInterrupt:
        print("Simulation interrupted by user")
    
    finally:
        visualizer.close()
    
    print("Real-time visualization test completed!")


def test_pheromone_field_evolution():
    """Test visualization of pheromone field evolution."""
    print("\nTesting pheromone field evolution visualization...")
    
    config = SimulationConfig(maze_width=6, maze_height=6, obstacle_ratio=0.15)
    maze = Maze(config.maze_width, config.maze_height, config.obstacle_ratio, seed=456)

    # Find valid positions
    start_pos = None
    target_pos = None
    for y in range(maze.height):
        for x in range(maze.width):
            if not maze.is_obstacle(x, y):
                if start_pos is None:
                    start_pos = (x, y)
                elif target_pos is None and (x, y) != start_pos:
                    target_pos = (x, y)
                    break
        if target_pos is not None:
            break

    maze.set_start_position(start_pos[0], start_pos[1])
    maze.set_target_position(target_pos[0], target_pos[1])
    
    pheromone_field = PheromoneField(config.maze_width, config.maze_height, config)
    pheromone_field.set_target_position(target_pos[0], target_pos[1])
    
    visualizer = Visualizer(maze, pheromone_field, config)
    
    print("1. Initial state (target pheromones only)...")
    pheromone_field.generate_target_pheromone_field()
    visualizer.show_pheromone_field('target', show_values=True)
    
    print("2. After adding obstacle pheromones...")
    # Add some obstacle pheromones manually
    pheromone_field.add_obstacle_pheromone(2, 2, intensity=-15.0)
    pheromone_field.add_obstacle_pheromone(3, 1, intensity=-10.0)
    pheromone_field.update_field()
    visualizer.show_pheromone_field('obstacle', show_values=True)
    
    print("3. Combined pheromone field...")
    visualizer.show_pheromone_field('combined', show_values=True)
    
    visualizer.close()
    print("Pheromone field evolution test completed!")


def test_statistics_tracking():
    """Test statistics collection and display."""
    print("\nTesting statistics tracking...")
    
    config = SimulationConfig(maze_width=8, maze_height=8, obstacle_ratio=0.3)
    maze = Maze(config.maze_width, config.maze_height, config.obstacle_ratio, seed=789)

    # Find valid positions
    start_pos = None
    target_pos = None
    for y in range(maze.height):
        for x in range(maze.width):
            if not maze.is_obstacle(x, y):
                if start_pos is None:
                    start_pos = (x, y)
                elif target_pos is None and (x, y) != start_pos:
                    target_pos = (x, y)
                    break
        if target_pos is not None:
            break

    maze.set_start_position(start_pos[0], start_pos[1])
    maze.set_target_position(target_pos[0], target_pos[1])

    pheromone_field = PheromoneField(config.maze_width, config.maze_height, config)
    pheromone_field.set_target_position(target_pos[0], target_pos[1])
    pheromone_field.generate_target_pheromone_field()

    agent = Agent(start_pos[0], start_pos[1], maze, pheromone_field, config)
    visualizer = Visualizer(maze, pheromone_field, config)
    
    # Simulate navigation to collect statistics
    for step in range(20):
        # Update statistics (this happens automatically in render_frame)
        visualizer._update_statistics_display(agent)
        
        # Execute move
        agent.execute_pheromone_based_move()
        pheromone_field.update_field()
    
    # Get statistics summary
    stats = visualizer.get_statistics_summary()
    print("Statistics summary:")
    for key, value in stats.items():
        print(f"  {key}: {value}")
    
    # Display comprehensive statistics
    visualizer.display_statistics(agent, success=agent.is_at_target(), total_time=2.0)
    
    visualizer.close()
    print("Statistics tracking test completed!")


def main():
    """Run all visualization tests."""
    print("=== Pheromone Maze Visualization System Tests ===\n")
    
    try:
        # Test 1: Basic visualization features
        test_basic_visualization()
        
        # Test 2: Real-time visualization
        test_real_time_visualization()
        
        # Test 3: Pheromone field evolution
        test_pheromone_field_evolution()
        
        # Test 4: Statistics tracking
        test_statistics_tracking()
        
        print("\n=== All visualization tests completed successfully! ===")
        
    except Exception as e:
        print(f"\nError during testing: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
