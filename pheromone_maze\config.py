"""
Configuration data classes for the pheromone maze navigation system.
"""

from dataclasses import dataclass, asdict
from typing import <PERSON><PERSON>, Dict, Any, Optional
import numpy as np
import json
import yaml
from pathlib import Path


@dataclass
class SimulationConfig:
    """Configuration parameters for the maze navigation simulation."""
    
    # Maze parameters
    maze_width: int = 20
    maze_height: int = 20
    obstacle_ratio: float = 0.3
    
    # Pheromone parameters
    pheromone_decay_rate: float = 0.95
    pheromone_diffusion_rate: float = 0.1
    obstacle_pheromone_intensity: float = -10.0
    target_pheromone_intensity: float = 15.0
    
    # Simulation parameters
    max_steps: int = 1000
    visualization_delay: float = 0.1
    
    # Agent parameters
    decision_noise_std: float = 0.1
    
    def __post_init__(self):
        """Validate configuration parameters."""
        if self.maze_width < 3 or self.maze_height < 3:
            raise ValueError("Maze dimensions must be at least 3x3")

        if not (0.1 <= self.obstacle_ratio <= 0.8):
            raise ValueError("Obstacle ratio must be between 0.1 and 0.8")

        if self.pheromone_decay_rate <= 0 or self.pheromone_decay_rate > 1:
            raise ValueError("Pheromone decay rate must be between 0 and 1")

        if self.pheromone_diffusion_rate < 0 or self.pheromone_diffusion_rate > 1:
            raise ValueError("Pheromone diffusion rate must be between 0 and 1")

        if self.max_steps <= 0:
            raise ValueError("Max steps must be positive")

        if self.visualization_delay < 0:
            raise ValueError("Visualization delay must be non-negative")

    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'SimulationConfig':
        """Create configuration from dictionary."""
        # Filter out unknown keys
        valid_keys = {field.name for field in cls.__dataclass_fields__.values()}
        filtered_dict = {k: v for k, v in config_dict.items() if k in valid_keys}
        return cls(**filtered_dict)

    @classmethod
    def from_file(cls, config_path: str) -> 'SimulationConfig':
        """Load configuration from JSON or YAML file."""
        config_path = Path(config_path)

        if not config_path.exists():
            raise FileNotFoundError(f"Configuration file not found: {config_path}")

        with open(config_path, 'r', encoding='utf-8') as f:
            if config_path.suffix.lower() in ['.yaml', '.yml']:
                config_dict = yaml.safe_load(f)
            elif config_path.suffix.lower() == '.json':
                config_dict = json.load(f)
            else:
                raise ValueError(f"Unsupported configuration file format: {config_path.suffix}")

        return cls.from_dict(config_dict)

    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary."""
        return asdict(self)

    def save_to_file(self, config_path: str) -> None:
        """Save configuration to JSON or YAML file."""
        config_path = Path(config_path)
        config_dict = self.to_dict()

        with open(config_path, 'w', encoding='utf-8') as f:
            if config_path.suffix.lower() in ['.yaml', '.yml']:
                yaml.dump(config_dict, f, default_flow_style=False, indent=2)
            elif config_path.suffix.lower() == '.json':
                json.dump(config_dict, f, indent=2)
            else:
                raise ValueError(f"Unsupported configuration file format: {config_path.suffix}")

    def update_from_dict(self, updates: Dict[str, Any]) -> 'SimulationConfig':
        """Create new configuration with updated values."""
        current_dict = self.to_dict()
        current_dict.update(updates)
        return self.from_dict(current_dict)


@dataclass
class PheromoneData:
    """Data structure for storing pheromone field information."""
    
    obstacle_field: np.ndarray  # Obstacle pheromone field (negative values)
    target_field: np.ndarray    # Target pheromone field (positive values)
    combined_field: np.ndarray  # Combined pheromone field
    
    def __post_init__(self):
        """Validate pheromone data arrays."""
        if not (self.obstacle_field.shape == self.target_field.shape == self.combined_field.shape):
            raise ValueError("All pheromone field arrays must have the same shape")


@dataclass
class AgentState:
    """State information for an agent."""
    
    x: int
    y: int
    move_count: int
    path_history: list  # List[Tuple[int, int]]
    last_pheromone_perception: dict  # Dict[str, float]
    
    def get_position(self) -> Tuple[int, int]:
        """Get current position as a tuple."""
        return (self.x, self.y)
    
    def add_to_path(self, x: int, y: int) -> None:
        """Add a position to the path history."""
        self.path_history.append((x, y))


@dataclass
class ExperimentResult:
    """Results from a single navigation experiment."""

    # Experiment parameters
    experiment_id: str
    config: SimulationConfig

    # Maze information
    maze_width: int
    maze_height: int
    start_position: Tuple[int, int]
    target_position: Tuple[int, int]
    obstacle_count: int

    # Navigation results
    success: bool
    steps_taken: int
    total_time: float
    collision_count: int
    path_length: int

    # Efficiency metrics
    manhattan_distance: int
    path_efficiency: float  # manhattan_distance / path_length

    # Additional statistics
    unique_positions_visited: int
    backtrack_count: int

    def to_dict(self) -> Dict[str, Any]:
        """Convert result to dictionary for serialization."""
        result_dict = asdict(self)
        # Convert config to dict as well
        result_dict['config'] = self.config.to_dict()
        return result_dict

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ExperimentResult':
        """Create result from dictionary."""
        # Reconstruct config
        config_data = data.pop('config')
        config = SimulationConfig.from_dict(config_data)
        data['config'] = config
        return cls(**data)


@dataclass
class BatchExperimentResults:
    """Results from a batch of navigation experiments."""

    batch_id: str
    total_experiments: int
    successful_experiments: int
    failed_experiments: int

    # Aggregate statistics
    average_steps: float
    average_time: float
    average_collisions: float
    average_efficiency: float

    # Success rate
    success_rate: float

    # Individual results
    results: list  # List[ExperimentResult]

    def add_result(self, result: ExperimentResult) -> None:
        """Add a single experiment result to the batch."""
        self.results.append(result)
        self.total_experiments += 1

        if result.success:
            self.successful_experiments += 1
        else:
            self.failed_experiments += 1

        # Recalculate aggregate statistics
        self._calculate_statistics()

    def _calculate_statistics(self) -> None:
        """Calculate aggregate statistics from all results."""
        if not self.results:
            return

        self.success_rate = self.successful_experiments / self.total_experiments

        # Calculate averages
        self.average_steps = sum(r.steps_taken for r in self.results) / len(self.results)
        self.average_time = sum(r.total_time for r in self.results) / len(self.results)
        self.average_collisions = sum(r.collision_count for r in self.results) / len(self.results)
        self.average_efficiency = sum(r.path_efficiency for r in self.results) / len(self.results)

    def get_summary(self) -> Dict[str, Any]:
        """Get summary statistics."""
        return {
            'batch_id': self.batch_id,
            'total_experiments': self.total_experiments,
            'success_rate': self.success_rate,
            'successful_experiments': self.successful_experiments,
            'failed_experiments': self.failed_experiments,
            'average_steps': self.average_steps,
            'average_time': self.average_time,
            'average_collisions': self.average_collisions,
            'average_efficiency': self.average_efficiency
        }

    def save_to_file(self, filepath: str) -> None:
        """Save batch results to file."""
        filepath = Path(filepath)

        # Prepare data for serialization
        data = {
            'batch_summary': self.get_summary(),
            'individual_results': [result.to_dict() for result in self.results]
        }

        with open(filepath, 'w', encoding='utf-8') as f:
            if filepath.suffix.lower() == '.json':
                json.dump(data, f, indent=2)
            elif filepath.suffix.lower() in ['.yaml', '.yml']:
                yaml.dump(data, f, default_flow_style=False, indent=2)
            else:
                raise ValueError(f"Unsupported file format: {filepath.suffix}")

    @classmethod
    def load_from_file(cls, filepath: str) -> 'BatchExperimentResults':
        """Load batch results from file."""
        filepath = Path(filepath)

        with open(filepath, 'r', encoding='utf-8') as f:
            if filepath.suffix.lower() == '.json':
                data = json.load(f)
            elif filepath.suffix.lower() in ['.yaml', '.yml']:
                data = yaml.safe_load(f)
            else:
                raise ValueError(f"Unsupported file format: {filepath.suffix}")

        # Reconstruct batch results
        summary = data['batch_summary']
        results = [ExperimentResult.from_dict(r) for r in data['individual_results']]

        batch = cls(
            batch_id=summary['batch_id'],
            total_experiments=summary['total_experiments'],
            successful_experiments=summary['successful_experiments'],
            failed_experiments=summary['failed_experiments'],
            average_steps=summary['average_steps'],
            average_time=summary['average_time'],
            average_collisions=summary['average_collisions'],
            average_efficiency=summary['average_efficiency'],
            success_rate=summary['success_rate'],
            results=results
        )

        return batch