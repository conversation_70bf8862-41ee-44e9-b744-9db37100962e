#!/usr/bin/env python3
"""
Demonstration script for the pheromone maze visualization system.

This script shows a complete navigation simulation with real-time visualization.
"""

import sys
import time
from pheromone_maze.config import SimulationConfig
from pheromone_maze.maze import Maze
from pheromone_maze.pheromone_field import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from pheromone_maze.agent import Agent
from pheromone_maze.visualizer import Visualizer


def run_navigation_demo():
    """Run a complete navigation demonstration with visualization."""
    print("=== Pheromone Maze Navigation Demo ===\n")
    
    # Create configuration
    config = SimulationConfig(
        maze_width=12,
        maze_height=12,
        obstacle_ratio=0.25,
        visualization_delay=0.3,  # Slower for better observation
        max_steps=100,
        pheromone_decay_rate=0.98,
        pheromone_diffusion_rate=0.15
    )
    
    print(f"Creating {config.maze_width}x{config.maze_height} maze with {config.obstacle_ratio*100:.0f}% obstacles...")
    
    # Create maze
    maze = Maze(config.maze_width, config.maze_height, config.obstacle_ratio, seed=42)
    
    # Find good start and target positions (far apart)
    start_pos = None
    target_pos = None
    max_distance = 0
    
    # Find all open positions
    open_positions = []
    for y in range(maze.height):
        for x in range(maze.width):
            if not maze.is_obstacle(x, y):
                open_positions.append((x, y))
    
    # Find the pair with maximum Manhattan distance
    for i, pos1 in enumerate(open_positions):
        for pos2 in open_positions[i+1:]:
            distance = abs(pos1[0] - pos2[0]) + abs(pos1[1] - pos2[1])
            if distance > max_distance:
                max_distance = distance
                start_pos = pos1
                target_pos = pos2
    
    if start_pos is None or target_pos is None:
        print("Error: Could not find valid start and target positions")
        return 1
    
    maze.set_start_position(start_pos[0], start_pos[1])
    maze.set_target_position(target_pos[0], target_pos[1])
    
    print(f"Start position: {start_pos}")
    print(f"Target position: {target_pos}")
    print(f"Manhattan distance: {max_distance}")
    print()
    
    # Create pheromone field
    pheromone_field = PheromoneField(config.maze_width, config.maze_height, config)
    pheromone_field.set_target_position(target_pos[0], target_pos[1])
    pheromone_field.generate_target_pheromone_field()
    
    # Create agent
    agent = Agent(start_pos[0], start_pos[1], maze, pheromone_field, config)
    
    # Create visualizer
    visualizer = Visualizer(maze, pheromone_field, config)
    visualizer.set_interactive_mode(True)
    
    print("Initializing visualization...")
    visualizer.initialize_display(show_pheromones=True, show_separate_fields=False)
    
    print("Starting navigation simulation...")
    print("The agent will navigate using pheromone gradients.")
    print("- Blue circle: Agent")
    print("- Gold star: Target")
    print("- Black squares: Obstacles")
    print("- Blue line: Agent's path")
    print("- Heatmap: Combined pheromone field (red=repulsive, green=attractive)")
    print("\nClose the matplotlib window to end the simulation.\n")
    
    # Run simulation
    step_count = 0
    start_time = time.time()
    success = False
    
    try:
        while step_count < config.max_steps:
            # Record frame for potential animation
            visualizer.record_frame(agent)
            
            # Render current state
            visualizer.render_frame(agent, show_values=False)
            
            # Check if target reached
            if agent.is_at_target():
                success = True
                print(f"\n🎉 SUCCESS! Agent reached target in {step_count} steps!")
                break
            
            # Execute agent move
            move_success = agent.execute_pheromone_based_move()
            
            # Update pheromone field
            pheromone_field.update_field()
            
            step_count += 1
            
            # Print progress every 10 steps
            if step_count % 10 == 0:
                print(f"Step {step_count}: Agent at {agent.get_position()}, "
                      f"Collisions: {agent.get_collision_count()}")
            
            # Check if agent is stuck
            if not move_success:
                recent_positions = agent.get_move_history()[-10:] if len(agent.get_move_history()) >= 10 else agent.get_move_history()
                if len(set(recent_positions)) <= 2:  # Agent is oscillating
                    print(f"\nAgent appears stuck after {step_count} steps")
                    break
        
        # Final render with values shown
        visualizer.render_frame(agent, show_values=True)
        
        # Calculate final statistics
        end_time = time.time()
        total_time = end_time - start_time
        
        print(f"\n=== Simulation Results ===")
        print(f"Success: {success}")
        print(f"Steps taken: {step_count}")
        print(f"Collisions: {agent.get_collision_count()}")
        print(f"Path length: {len(agent.get_move_history())}")
        print(f"Simulation time: {total_time:.2f} seconds")
        
        if success:
            efficiency = max_distance / len(agent.get_move_history()) * 100
            print(f"Path efficiency: {efficiency:.1f}% (optimal={max_distance}, actual={len(agent.get_move_history())})")
        
        # Show comprehensive statistics
        print("\nDisplaying detailed statistics...")
        visualizer.display_statistics(agent, success, total_time)
        
        # Show final path
        print("Displaying final path...")
        visualizer.show_agent_path(agent, highlight_collisions=True)
        
        # Show final pheromone field
        print("Displaying final pheromone field...")
        visualizer.show_pheromone_field('combined', show_values=True)
        
    except KeyboardInterrupt:
        print("\nSimulation interrupted by user")
    except Exception as e:
        print(f"\nError during simulation: {e}")
        import traceback
        traceback.print_exc()
    finally:
        visualizer.close()
    
    print("\nDemo completed!")
    return 0


def main():
    """Main entry point."""
    try:
        return run_navigation_demo()
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
