#!/usr/bin/env python3
"""
Main program for the pheromone maze navigation system.

This is the primary entry point that integrates all components and provides
command-line interface for running navigation experiments.
"""

import sys
import argparse
import time
import random
import uuid
from pathlib import Path
from typing import Optional, Dict, Any, List

from pheromone_maze.config import Simu<PERSON><PERSON><PERSON>fig, ExperimentResult, BatchExperimentResults
from pheromone_maze.maze import Maze
from pheromone_maze.pheromone_field import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from pheromone_maze.agent import Agent
from pheromone_maze.visualizer import Visualizer


def create_argument_parser() -> argparse.ArgumentParser:
    """Create command line argument parser."""
    parser = argparse.ArgumentParser(
        description="Pheromone-based maze navigation system",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s --demo                           # Run interactive demo
  %(prog)s --batch 10                       # Run 10 experiments
  %(prog)s --config config.yaml             # Use custom configuration
  %(prog)s --width 15 --height 15 --obstacles 0.3  # Custom maze
  %(prog)s --batch 50 --output results.json # Batch with results output
        """
    )
    
    # Execution modes
    mode_group = parser.add_mutually_exclusive_group()
    mode_group.add_argument('--demo', action='store_true',
                           help='Run interactive demonstration')
    mode_group.add_argument('--batch', type=int, metavar='N',
                           help='Run N batch experiments')
    mode_group.add_argument('--single', action='store_true', default=True,
                           help='Run single experiment (default)')
    
    # Configuration
    parser.add_argument('--config', type=str, metavar='FILE',
                       help='Configuration file (JSON or YAML)')
    parser.add_argument('--save-config', type=str, metavar='FILE',
                       help='Save current configuration to file')
    
    # Maze parameters
    maze_group = parser.add_argument_group('maze parameters')
    maze_group.add_argument('--width', type=int, metavar='W',
                           help='Maze width (default: 20)')
    maze_group.add_argument('--height', type=int, metavar='H',
                           help='Maze height (default: 20)')
    maze_group.add_argument('--obstacles', type=float, metavar='RATIO',
                           help='Obstacle ratio 0.1-0.8 (default: 0.3)')
    
    # Pheromone parameters
    pheromone_group = parser.add_argument_group('pheromone parameters')
    pheromone_group.add_argument('--decay-rate', type=float, metavar='RATE',
                                help='Pheromone decay rate 0-1 (default: 0.95)')
    pheromone_group.add_argument('--diffusion-rate', type=float, metavar='RATE',
                                help='Pheromone diffusion rate 0-1 (default: 0.1)')
    pheromone_group.add_argument('--obstacle-intensity', type=float, metavar='INTENSITY',
                                help='Obstacle pheromone intensity (default: -10.0)')
    pheromone_group.add_argument('--target-intensity', type=float, metavar='INTENSITY',
                                help='Target pheromone intensity (default: 15.0)')
    
    # Simulation parameters
    sim_group = parser.add_argument_group('simulation parameters')
    sim_group.add_argument('--max-steps', type=int, metavar='STEPS',
                          help='Maximum steps per experiment (default: 1000)')
    sim_group.add_argument('--noise', type=float, metavar='STD',
                          help='Decision noise standard deviation (default: 0.1)')
    
    # Visualization parameters
    vis_group = parser.add_argument_group('visualization parameters')
    vis_group.add_argument('--no-visualization', action='store_true',
                          help='Disable visualization')
    vis_group.add_argument('--delay', type=float, metavar='SECONDS',
                          help='Visualization delay (default: 0.1)')
    vis_group.add_argument('--show-pheromones', action='store_true',
                          help='Show pheromone fields in visualization')
    vis_group.add_argument('--show-values', action='store_true',
                          help='Show pheromone values in visualization')
    
    # Output parameters
    output_group = parser.add_argument_group('output parameters')
    output_group.add_argument('--output', type=str, metavar='FILE',
                             help='Output file for results (JSON or YAML)')
    output_group.add_argument('--verbose', '-v', action='store_true',
                             help='Verbose output')
    output_group.add_argument('--quiet', '-q', action='store_true',
                             help='Quiet mode (minimal output)')
    
    # Random seed
    parser.add_argument('--seed', type=int, metavar='SEED',
                       help='Random seed for reproducible results')
    
    return parser


def load_configuration(args: argparse.Namespace) -> SimulationConfig:
    """Load and merge configuration from file and command line arguments."""
    # Start with default configuration
    if args.config:
        try:
            config = SimulationConfig.from_file(args.config)
            if not args.quiet:
                print(f"Loaded configuration from: {args.config}")
        except Exception as e:
            print(f"Warning: Failed to load config file {args.config}: {e}")
            print("Using default configuration")
            config = SimulationConfig()
    else:
        config = SimulationConfig()
    
    # Override with command line arguments
    updates = {}
    
    if args.width is not None:
        updates['maze_width'] = args.width
    if args.height is not None:
        updates['maze_height'] = args.height
    if args.obstacles is not None:
        updates['obstacle_ratio'] = args.obstacles
    if args.decay_rate is not None:
        updates['pheromone_decay_rate'] = args.decay_rate
    if args.diffusion_rate is not None:
        updates['pheromone_diffusion_rate'] = args.diffusion_rate
    if args.obstacle_intensity is not None:
        updates['obstacle_pheromone_intensity'] = args.obstacle_intensity
    if args.target_intensity is not None:
        updates['target_pheromone_intensity'] = args.target_intensity
    if args.max_steps is not None:
        updates['max_steps'] = args.max_steps
    if args.noise is not None:
        updates['decision_noise_std'] = args.noise
    if args.delay is not None:
        updates['visualization_delay'] = args.delay
    
    if updates:
        config = config.update_from_dict(updates)
        if not args.quiet:
            print("Applied command line parameter overrides")
    
    return config


def validate_configuration(config: SimulationConfig) -> bool:
    """Validate configuration and print warnings for potential issues."""
    warnings = []
    
    # Check for potentially problematic parameter combinations
    if config.obstacle_ratio > 0.6:
        warnings.append(f"High obstacle ratio ({config.obstacle_ratio:.2f}) may make navigation very difficult")
    
    if config.pheromone_decay_rate < 0.8:
        warnings.append(f"Low decay rate ({config.pheromone_decay_rate:.2f}) may cause pheromone buildup")
    
    if config.pheromone_diffusion_rate > 0.3:
        warnings.append(f"High diffusion rate ({config.pheromone_diffusion_rate:.2f}) may blur gradients")
    
    if config.max_steps < 100:
        warnings.append(f"Low max steps ({config.max_steps}) may not allow enough time for navigation")
    
    if warnings:
        print("Configuration warnings:")
        for warning in warnings:
            print(f"  - {warning}")
        print()
    
    return True


def run_single_experiment(config: SimulationConfig, 
                         experiment_id: str,
                         enable_visualization: bool = True,
                         show_pheromones: bool = False,
                         show_values: bool = False,
                         verbose: bool = False) -> ExperimentResult:
    """Run a single navigation experiment."""
    if verbose:
        print(f"Starting experiment {experiment_id}")
    
    # Create maze
    maze = Maze(config.maze_width, config.maze_height, config.obstacle_ratio)
    
    # Get start and target positions (maze generates them automatically)
    start_pos = maze.get_start_position()
    target_pos = maze.get_target_position()

    # If positions are not set or too close, find better ones
    if (start_pos is None or target_pos is None or
        abs(start_pos[0] - target_pos[0]) + abs(start_pos[1] - target_pos[1]) < 3):

        # Find all open positions
        open_positions = []
        for y in range(maze.height):
            for x in range(maze.width):
                if not maze.is_obstacle(x, y):
                    open_positions.append((x, y))

        if len(open_positions) < 2:
            raise RuntimeError("Not enough open positions in maze")

        # Find positions with good distance
        import random
        max_distance = 0
        best_start = None
        best_target = None

        for i, pos1 in enumerate(open_positions):
            for pos2 in open_positions[i+1:]:
                distance = abs(pos1[0] - pos2[0]) + abs(pos1[1] - pos2[1])
                if distance > max_distance:
                    max_distance = distance
                    best_start = pos1
                    best_target = pos2

        start_pos = best_start if best_start else open_positions[0]
        target_pos = best_target if best_target else open_positions[-1]

        maze.set_start_position(start_pos[0], start_pos[1])
        maze.set_target_position(target_pos[0], target_pos[1])
    
    manhattan_distance = abs(start_pos[0] - target_pos[0]) + abs(start_pos[1] - target_pos[1])
    
    if verbose:
        print(f"  Start: {start_pos}, Target: {target_pos}, Distance: {manhattan_distance}")
    
    # Create pheromone field
    pheromone_field = PheromoneField(config.maze_width, config.maze_height, config)
    pheromone_field.set_target_position(target_pos[0], target_pos[1])
    pheromone_field.generate_target_pheromone_field()
    
    # Create agent
    agent = Agent(start_pos[0], start_pos[1], maze, pheromone_field, config)
    
    # Create visualizer if needed
    visualizer = None
    if enable_visualization:
        visualizer = Visualizer(maze, pheromone_field, config)
        visualizer.set_interactive_mode(False)
        visualizer.initialize_display(show_pheromones=show_pheromones, 
                                     show_separate_fields=False)
    
    # Run simulation
    start_time = time.time()
    step_count = 0
    success = False
    
    try:
        while step_count < config.max_steps:
            if visualizer:
                visualizer.record_frame(agent)
                visualizer.render_frame(agent, show_values=show_values)
            
            # Check if target reached
            if agent.is_at_target():
                success = True
                break
            
            # Execute agent move
            agent.execute_pheromone_based_move()
            
            # Update pheromone field
            pheromone_field.update_field()
            
            step_count += 1
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # Calculate metrics
        path_length = len(agent.get_move_history())
        path_efficiency = manhattan_distance / path_length if path_length > 0 else 0.0
        
        # Count unique positions visited
        unique_positions = len(set(agent.get_move_history()))
        
        # Count backtracks (visiting same position multiple times)
        backtrack_count = path_length - unique_positions
        
        # Create result
        result = ExperimentResult(
            experiment_id=experiment_id,
            config=config,
            maze_width=config.maze_width,
            maze_height=config.maze_height,
            start_position=start_pos,
            target_position=target_pos,
            obstacle_count=maze.get_obstacle_count(),
            success=success,
            steps_taken=step_count,
            total_time=total_time,
            collision_count=agent.get_collision_count(),
            path_length=path_length,
            manhattan_distance=manhattan_distance,
            path_efficiency=path_efficiency,
            unique_positions_visited=unique_positions,
            backtrack_count=backtrack_count
        )
        
        if verbose:
            print(f"  Result: {'SUCCESS' if success else 'FAILED'} in {step_count} steps")
        
        return result
        
    finally:
        if visualizer:
            visualizer.close()


def run_batch_experiments(config: SimulationConfig,
                         num_experiments: int,
                         output_file: Optional[str] = None,
                         verbose: bool = False,
                         quiet: bool = False) -> BatchExperimentResults:
    """Run a batch of navigation experiments."""
    batch_id = str(uuid.uuid4())[:8]

    if not quiet:
        print(f"Running batch of {num_experiments} experiments (ID: {batch_id})")
        print(f"Configuration: {config.maze_width}x{config.maze_height} maze, "
              f"{config.obstacle_ratio:.1%} obstacles")

    # Initialize batch results
    batch_results = BatchExperimentResults(
        batch_id=batch_id,
        total_experiments=0,
        successful_experiments=0,
        failed_experiments=0,
        average_steps=0.0,
        average_time=0.0,
        average_collisions=0.0,
        average_efficiency=0.0,
        success_rate=0.0,
        results=[]
    )

    # Run experiments
    for i in range(num_experiments):
        experiment_id = f"{batch_id}-{i+1:03d}"

        if verbose:
            print(f"\nExperiment {i+1}/{num_experiments}")
        elif not quiet and (i+1) % 10 == 0:
            print(f"Completed {i+1}/{num_experiments} experiments...")

        try:
            result = run_single_experiment(
                config=config,
                experiment_id=experiment_id,
                enable_visualization=False,  # Disable visualization for batch
                verbose=verbose
            )
            batch_results.add_result(result)

        except Exception as e:
            if not quiet:
                print(f"Error in experiment {i+1}: {e}")
            continue

    if not quiet:
        print(f"\nBatch completed: {batch_results.successful_experiments}/{num_experiments} successful")
        print(f"Success rate: {batch_results.success_rate:.1%}")
        print(f"Average steps: {batch_results.average_steps:.1f}")
        print(f"Average efficiency: {batch_results.average_efficiency:.1%}")

    # Save results if requested
    if output_file:
        try:
            batch_results.save_to_file(output_file)
            if not quiet:
                print(f"Results saved to: {output_file}")
        except Exception as e:
            print(f"Error saving results: {e}")

    return batch_results


def run_demo(config: SimulationConfig,
             show_pheromones: bool = True,
             show_values: bool = False,
             verbose: bool = False) -> ExperimentResult:
    """Run interactive demonstration."""
    print("=== Pheromone Maze Navigation Demo ===\n")

    if verbose:
        print("Configuration:")
        print(f"  Maze size: {config.maze_width}x{config.maze_height}")
        print(f"  Obstacle ratio: {config.obstacle_ratio:.1%}")
        print(f"  Max steps: {config.max_steps}")
        print(f"  Pheromone decay: {config.pheromone_decay_rate:.2f}")
        print(f"  Pheromone diffusion: {config.pheromone_diffusion_rate:.2f}")
        print()

    result = run_single_experiment(
        config=config,
        experiment_id="demo",
        enable_visualization=True,
        show_pheromones=show_pheromones,
        show_values=show_values,
        verbose=verbose
    )

    print(f"\n=== Demo Results ===")
    print(f"Success: {result.success}")
    print(f"Steps taken: {result.steps_taken}")
    print(f"Collisions: {result.collision_count}")
    print(f"Path efficiency: {result.path_efficiency:.1%}")
    print(f"Time: {result.total_time:.2f} seconds")

    return result


def main() -> int:
    """Main entry point."""
    parser = create_argument_parser()
    args = parser.parse_args()

    # Set random seed if provided
    if args.seed is not None:
        random.seed(args.seed)
        import numpy as np
        np.random.seed(args.seed)
        if not args.quiet:
            print(f"Random seed set to: {args.seed}")

    try:
        # Load configuration
        config = load_configuration(args)

        # Validate configuration
        if not validate_configuration(config):
            return 1

        # Save configuration if requested
        if args.save_config:
            config.save_to_file(args.save_config)
            if not args.quiet:
                print(f"Configuration saved to: {args.save_config}")

        # Determine execution mode and run
        if args.demo:
            run_demo(
                config=config,
                show_pheromones=args.show_pheromones,
                show_values=args.show_values,
                verbose=args.verbose
            )
        elif args.batch:
            run_batch_experiments(
                config=config,
                num_experiments=args.batch,
                output_file=args.output,
                verbose=args.verbose,
                quiet=args.quiet
            )
        else:
            # Single experiment
            enable_viz = not args.no_visualization
            result = run_single_experiment(
                config=config,
                experiment_id="single",
                enable_visualization=enable_viz,
                show_pheromones=args.show_pheromones,
                show_values=args.show_values,
                verbose=args.verbose
            )

            if not args.quiet:
                print(f"\n=== Experiment Results ===")
                print(f"Success: {result.success}")
                print(f"Steps: {result.steps_taken}/{config.max_steps}")
                print(f"Collisions: {result.collision_count}")
                print(f"Path efficiency: {result.path_efficiency:.1%}")
                print(f"Time: {result.total_time:.2f} seconds")

            # Save single result if requested
            if args.output:
                try:
                    import json
                    with open(args.output, 'w') as f:
                        json.dump(result.to_dict(), f, indent=2)
                    if not args.quiet:
                        print(f"Result saved to: {args.output}")
                except Exception as e:
                    print(f"Error saving result: {e}")

        return 0

    except KeyboardInterrupt:
        print("\nInterrupted by user")
        return 1
    except Exception as e:
        print(f"Error: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
